<template>
  <div class="detail-container">
    <div class="detail-header">
      <h3>排污单位登记信息-排污节点及污染治理设施</h3>
    </div>
    <div class="detail-content">
      <header style="margin-bottom: 12px; font-size: 14px; color: #42b2de;">
        一、废气产排污节点、污染物及污染治理设施信息表
      </header>
      <!-- 说明部分 -->
      <div class="instructions">
        <header class="instruction-header">说明</header>
        <div class="instruction-item">
          (1) 产污设施名称：只产生污染物的生产设施等设施。
        </div>
        <div class="instruction-item">
          (2) 对应产污环节名称：指生产设施对应的主要产污环节名称。
        </div>
        <div class="instruction-item">
          (3)
          污染物种类：指产生的主要污染物类型，以相应排放标准中确定的污染因子为准。
        </div>
        <div class="instruction-item">
          (4) 排放形式：指有组织排放或无组织排放。
        </div>
        <div class="instruction-item">
          (5)
          污染治理设施名称：对于有组织废气，以火电行业为例，污染治理设施名称包括三电场静电除尘器、四电场静电除尘器、普通袋式除尘器、覆膜滤料袋式除尘器等。
        </div>
        <div class="instruction-item">
          (6)
          有组织排放口编号请填写已有在线监测排放口编号或执法监测使用编号，若无相关编号可按照《固定污染源（水、大气）编码规侧（试行）》中的排放口编码规侧编写，如DA001。
        </div>
        <div class="instruction-item">
          (7)
          排放口设置是否符合要求：指排放口设置是否符合排污口规范化整治技术要求等相关文件的规定。
        </div>
      </div>
      <div class="table-container">
        <table class="capacity-table">
          <thead>
            <!-- 第一行表头 -->
            <tr>
              <th rowspan="2">产污设施编号</th>
              <th rowspan="2">产污设施名称</th>
              <th rowspan="2">对应产污环节名称</th>
              <th rowspan="2">污染物种类</th>
              <th rowspan="2">排放形式</th>
              <th colspan="5">污染治理设施</th>
              <th rowspan="2">有组织排放口编号</th>
              <th rowspan="2">有组织排放口名称</th>
              <th rowspan="2">排放口设置是否符合要求</th>
              <th rowspan="2">排放口类型</th>
              <th rowspan="2">其他信息</th>
            </tr>
            <!-- 第二行表头 -->
            <tr>
              <th>污染防治设施编号</th>
              <th>污染防治设施名称</th>
              <th>污染治理设施工艺</th>
              <th>是否为可行技术</th>
              <th>污染治理设施其他信息</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="zlssInfoList.length === 0">
              <td colspan="15" class="no-data">暂无数据</td>
            </tr>
            <tr v-for="item in zlssInfoList" :key="item.id">
              <!--  产污设施编号-->
              <td>{{ item.deviceCode || "-" }}</td>
              <!--  产污设施名称-->
              <td>{{ item.deviceName || "-" }}</td>
              <!--  对应产污环节名称-->
              <td>{{ item.cwName || "-" }}</td>
              <!--  污染物种类-->
              <td>{{ item.wrwName || "-" }}</td>
              <!--  排放形式-->
              <td>{{ item.emissionName || "-" }}</td>
              <!--  污染防治设施编号-->
              <td>{{ item.wrzlCode || "-" }}</td>
              <!--  污染防治设施名称-->
              <td>{{ item.wrzlName || "-" }}</td>
              <!--  污染治理设施工艺-->
              <td>{{ item.wrzlgyName || "-" }}</td>
              <!--  是否为可行技术-->
              <td>{{ item.isBestName || "-" }}</td>
              <!--  污染治理设施其他信息-->
              <td>{{ item.wrzlContent || "-" }}</td>
              <!--  有组织排放口编号-->
              <td>{{ item.drainCode || "-" }}</td>
              <!--  有组织排放口名称-->
              <td>{{ item.drainName || "-" }}</td>
              <!--  排放口设置是否符合要求-->
              <td>{{ item.isDemandName || "-" }}</td>
              <!--  排放口类型-->
              <td>{{ item.wrylxName || "-" }}</td>
              <!--  其他信息-->
              <td>{{ item.otherContent || "-" }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!--      废水表格-->
      <header style="margin-top: 30px; margin-bottom: 12px; font-size: 14px; color: #42b2de;">
        二、废水类别：指产生废水的工艺、工序，或废水类型的名称。
      </header>
      <!-- 说明部分 -->
      <div class="instructions">
        <header class="instruction-header">说明</header>
        <div class="instruction-item">
          (1) 废水类别：指产生废水的工艺、工序，或废水类型的名称。
        </div>
        <div class="instruction-item">
          (2)
          污染物种类：指产生的主要污染物类型，以相应排放标准中确定的污染因子为准。
        </div>
        <div class="instruction-item">
          (3)
          排放去向：包括不外排；排至厂内综合污水处理站；直接进入海域；直接进入江河、湖、库等水环境：
          进入城市下水道（再入江河、湖、库）；进入城市下水道（再入沿海海域）；进入城市污水处理厂；直接进入污灌农田；进入地渗或蒸发地；进入其他单位；工业废水集中处理厂；其他（包括回喷、回填、回
          灌、回用等)。对于工艺、工序产生的废水，“不外排指全部在工序内部循环使用，排至厂内综合污水处理站"指工序废水经处理后排至综合处理站。对于综合污水处理站，“不外排指全厂废水经处理后全部
          回用不排放。
        </div>
        <div class="instruction-item">
          (4)
          污染治理设施名称：指主要污水处理设施名称，如“综合污水处理站”、生活污水处理系统”等。
        </div>
        <div class="instruction-item">
          (5)
          排放口编号请填写已有在线监测排放口编号或执法监测使用编号，若无相关编号可按照《固定污染源（水、大气）编码规侧（试行）》中的排放口编码规测编写，如DW001。
        </div>
        <div class="instruction-item">
          (6)
          排放口设置是否符合要求：指排放口设置是否符合排污口规范化整治技术要求等相关文件的规定。
        </div>
        <div class="instruction-item">
          (7)
          除B06-B12以外的行业，若需填写与水处理通用工序相关的废水类别、污染物及污染治理设施信息表，行业类别请直接选择TY04。
        </div>
      </div>
      <div class="table-container">
        <table class="capacity-table">
          <thead>
            <!-- 第一行表头 -->
            <tr>
              <th rowspan="2">废水类别</th>
              <th rowspan="2">污染物种类</th>
              <th colspan="5">污染治理设施</th>
              <th rowspan="2">排放去向</th>
              <th rowspan="2">排放方式</th>
              <th rowspan="2">排放规律</th>
              <th rowspan="2">排放口编号</th>
              <th rowspan="2">排放口名称</th>
              <th rowspan="2">排放口设置是否符合要求</th>
              <th rowspan="2">排放口类型</th>
              <th rowspan="2">其他信息</th>
            </tr>
            <!-- 第二行表头 -->
            <tr>
              <th>污染治理设施编号</th>
              <th>污染治理设施名称</th>
              <th>污染治理设施工艺</th>
              <th>是否为可行技术</th>
              <th>污染治理设施其他信息</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="fswrwZlssInfoList.length === 0">
              <td colspan="14" class="no-data">暂无数据</td>
            </tr>
            <tr v-for="item in fswrwZlssInfoList" :key="item.id">
              <!--  废水类别-->
              <td>{{ item.fslyName || "-" }}</td>
              <!--  污染物种类-->
              <td>{{ item.wrwName || "-" }}</td>
              <!--  污染治理设施编号-->
              <td>{{ item.wrzlCode || "-" }}</td>
              <!--  污染治理设施名称-->
              <td>{{ item.wrzlName || "-" }}</td>
              <!--  污染治理设施工艺-->
              <td>{{ item.wrzlgyName || "-" }}</td>
              <!--  是否为可行技术-->
              <td>{{ item.isBestName || "-" }}</td>
              <!--  污染治理设施其他信息-->
              <td>{{ item.wrzlContent || "-" }}</td>
              <!--  排放去向-->
              <td>{{ item.pfqxName || "-" }}</td>
              <!--  排放方式-->
              <td>{{ item.fspffsName || "-" }}</td>
              <!--  排放规律-->
              <td>{{ item.pffsName || "-" }}</td>
              <!--  排放口编号-->
              <td>{{ item.drainCode || "-" }}</td>
              <!--  排放口名称-->
              <td>{{ item.drainName || "-" }}</td>
              <!--  排放口设置是否符合要求-->
              <td>{{ item.isDemandName || "-" }}</td>
              <!--  排放口类型-->
              <td>{{ item.wrylxName || "-" }}</td>
              <!--  其他信息-->
              <td>{{ item.otherContent || "-" }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ZlssInfoDetail",
  props: {
    raw: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    zlssInfoList() {
      if (this.raw && this.raw.zlssInfoList) {
        return this.raw.zlssInfoList;
      } else {
        return [];
      }
    },
    fswrwZlssInfoList() {
      if (this.raw && this.raw.fswrwZlssInfoList) {
        return this.raw.fswrwZlssInfoList;
      } else {
        return [];
      }
    }
  },
  methods: {}
};
</script>

<style scoped lang="scss">
.detail-container {
  height: 100%;

  .detail-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
  }

  .detail-content {
    height: calc(100% - 60px);
    padding: 16px;
    overflow: auto;

    .table-container {
      width: 100%;
      overflow-x: auto;

      .capacity-table {
        width: 100%;
        min-width: 1200px; // 确保表格有足够宽度显示所有列
        font-size: 12px;
        table-layout: fixed;
        border-collapse: collapse;
        border: 1px solid #dddddd;

        th {
          padding: 8px 12px;
          font-weight: 500;
          color: #333333;
          text-align: center;
          background-color: #f5f5f5;
          border: 1px solid #dddddd;
        }

        td {
          padding: 8px 12px;
          color: #333333;
          text-align: center;
          word-break: break-all;
          vertical-align: middle;
          background-color: #ffffff;
          border: 1px solid #dddddd;

          &.no-data {
            font-style: italic;
            color: #999999;
            text-align: center;
          }
        }

        tbody tr {
          min-height: 40px;

          &:hover {
            background-color: #fafafa;

            td {
              background-color: #fafafa;
            }
          }
        }
      }
    }

    // 说明部分样式
    .instructions {
      padding: 12px 16px;
      margin-bottom: 8px;
      color: #007bff;
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;

      .instruction-header {
        margin-bottom: 4px;
        font-size: 14px;
      }

      .instruction-item {
        margin-bottom: 4px;
        font-size: 12px;
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
