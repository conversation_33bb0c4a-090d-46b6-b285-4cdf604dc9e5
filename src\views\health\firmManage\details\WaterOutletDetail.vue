<template>
  <div class="detail-container">
    <div class="detail-header">
      <h3>水污染物排放信息-排放口</h3>
    </div>
    <div class="detail-content">
      <header style="margin-bottom: 12px; font-size: 14px; color: #42b2de;">
        一、废水直接排放口基本情况表
      </header>
      <!-- 说明部分 -->
      <div class="instructions">
        <header class="instruction-header">说明</header>
        <div class="instruction-item">
          (1)
          排放口地理坐标：对于直接排放至地表水体的排放口，指废水排出厂界处经纬度坐标；纳入管控的车间或车间处理设施排放口，指废水排出车间或车间处理设施处经纬度坐标；可通过点击"选择"按钮在GIS地图中点选后自动生成。
        </div>
        <div class="instruction-item">
          (2) 受纳自然水体名称：指受纳水体的名称如南沙河、太子河、温榆河等。
        </div>
        <div class="instruction-item">
          (3)
          受纳自然水体功能目标：指对于直接排放至地表水体的排放口，其所处受纳水体功能类别，如III类、IV类、V类等。
        </div>
        <div class="instruction-item">
          (4)
          汇入受纳自然水体处理坐标：对于直接排放至地表水体的排放口，指废水汇入地表水体处经纬度坐标；可通过点击"选择"按钮在GIS地图中点选后自动生成。
        </div>
        <div class="instruction-item">
          (5)
          废水向海洋排放的，应当填写岸边排放或深海排放。深海排放的，还应说明排污口的深度，与岸线直线距离。在"其他信息"列中填写。
        </div>
        <div class="instruction-item">
          (6) 若有本表格中无法囊括的信息，可根据实际情况填写在"其他信息"列中。
        </div>
      </div>
      <div class="table-container">
        <table class="capacity-table">
          <thead>
            <!-- 第一行表头 -->
            <tr>
              <th rowspan="2">排放口编号</th>
              <th rowspan="2">排放口名称</th>
              <th colspan="2">排放口地理位置</th>
              <th rowspan="2">排水去向</th>
              <th rowspan="2">排放规律</th>
              <th rowspan="2">间歇式排放时段</th>
              <th colspan="2">受纳自然水体信息</th>
              <th colspan="2">汇入受纳自然水体处地理坐标</th>
              <th rowspan="2">其他信息</th>
            </tr>
            <!-- 第二行表头 -->
            <tr>
              <th>经度</th>
              <th>纬度</th>
              <th>名称</th>
              <th>受纳水体功能目标</th>
              <th>经度</th>
              <th>纬度</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="waterOutletBasicInfos.length === 0">
              <td colspan="12" class="no-data">暂无数据</td>
            </tr>
            <tr v-for="item in waterOutletBasicInfos" :key="item.id">
              <!--  排放口编号-->
              <td>{{ item.drainCode || "-" }}</td>
              <!--  排放口名称-->
              <td>{{ item.drainName || "-" }}</td>
              <!--  经度（排放口地理位置）-->
              <td>{{ item.longitude || "-" }}</td>
              <!--  纬度（排放口地理位置）-->
              <td>{{ item.latitude || "-" }}</td>
              <!--  排水去向-->
              <td>{{ item.pfqxName || "-" }}</td>
              <!--  排放规律-->
              <td>{{ item.pffsName || "-" }}</td>
              <!--  间歇式排放时段-->
              <td>{{ item.emissionTime || "-" }}</td>
              <!--  名称（受纳自然水体信息）-->
              <td>{{ item.sewageName || "-" }}</td>
              <!--  受纳水体功能目标-->
              <td>{{ item.functionName || "-" }}</td>
              <!--  经度（汇入受纳自然水体处地理坐标）-->
              <td>{{ item.natureLng || "-" }}</td>
              <!--  纬度（汇入受纳自然水体处地理坐标）-->
              <td>{{ item.natureLat || "-" }}</td>
              <!--  其他信息-->
              <td>{{ item.remarks || "-" }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!--      入河排污口表格-->
      <header
        style="margin-top: 30px; margin-bottom: 12px; font-size: 14px; color: #42b2de;"
      >
        二、入河排污口信息
      </header>

      <div class="table-container">
        <table class="capacity-table">
          <thead>
            <!-- 第一行表头 -->
            <tr>
              <th rowspan="2">排放口编号</th>
              <th rowspan="2">排放口名称</th>
              <th colspan="3">入河排污口</th>
              <th rowspan="2">其他信息</th>
            </tr>
            <!-- 第二行表头 -->
            <tr>
              <th>名称</th>
              <th>编号</th>
              <th>批复文号</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="riverOutletInfos.length === 0">
              <td colspan="6" class="no-data">暂无数据</td>
            </tr>
            <tr v-for="item in riverOutletInfos" :key="item.id">
              <!--  排放口编号-->
              <td>{{ item.drainCode || "-" }}</td>
              <!--  排放口名称-->
              <td>{{ item.drainName || "-" }}</td>
              <!--  名称（入河排污口）-->
              <td>{{ item.riversName || "-" }}</td>
              <!--  编号（入河排污口）-->
              <td>{{ item.riversCode || "-" }}</td>
              <!--  批复文号-->
              <td>{{ item.riversReplyNum || "-" }}</td>
              <!--  其他信息-->
              <td>{{ item.information || "-" }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "WaterOutletDetail",
  props: {
    raw: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    waterOutletBasicInfos() {
      if (this.raw && this.raw.waterOutletBasicInfos) {
        return this.raw.waterOutletBasicInfos;
      } else {
        return [];
      }
    },
    riverOutletInfos() {
      if (this.raw && this.raw.riverOutletInfos) {
        return this.raw.riverOutletInfos;
      } else {
        return [];
      }
    }
  },
  methods: {}
};
</script>

<style scoped lang="scss">
.detail-container {
  height: 100%;

  .detail-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
  }

  .detail-content {
    height: calc(100% - 60px);
    padding: 16px;
    overflow: auto;

    .table-container {
      width: 100%;
      overflow-x: auto;

      .capacity-table {
        width: 100%;
        min-width: 1200px; // 确保表格有足够宽度显示所有列
        font-size: 12px;
        table-layout: fixed;
        border-collapse: collapse;
        border: 1px solid #dddddd;

        th {
          padding: 8px 12px;
          font-weight: 500;
          color: #333333;
          text-align: center;
          background-color: #f5f5f5;
          border: 1px solid #dddddd;
        }

        td {
          padding: 8px 12px;
          color: #333333;
          text-align: center;
          word-break: break-all;
          vertical-align: middle;
          background-color: #ffffff;
          border: 1px solid #dddddd;

          &.no-data {
            font-style: italic;
            color: #999999;
            text-align: center;
          }
        }

        tbody tr {
          min-height: 40px;

          &:hover {
            background-color: #fafafa;

            td {
              background-color: #fafafa;
            }
          }
        }
      }
    }

    // 说明部分样式
    .instructions {
      padding: 12px 16px;
      margin-bottom: 8px;
      color: #007bff;
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;

      .instruction-header {
        margin-bottom: 4px;
        font-size: 14px;
      }

      .instruction-item {
        margin-bottom: 4px;
        font-size: 12px;
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
