<template>
  <div class="detail-container">
    <div class="detail-header">
      <h3>大气污染物排放信息-排放口</h3>
    </div>
    <div class="detail-content">
      <!-- 说明部分 -->
      <div class="instructions">
        <header class="instruction-header">说明</header>
        <div class="instruction-item">
          (1)
          排放口地理坐标：指排气筒所在地经纬度坐标，可通过点击“选择按钮在GS地图中点选后自动生成。
        </div>
        <div class="instruction-item">
          (2) 排气筒出口内径：对于不规测形状排气筒，填写等效内径。
        </div>
        <div class="instruction-item">
          (3) 若有本表格无法囊括的信息，可根据实际情况填写在“其他信息”列中
        </div>
        <div class="instruction-item">
          (4) 锅炉排污单位请点击显示为蓝色的排放口编号按钮完成基准烟气量的计算。
        </div>
      </div>
      <div class="table-container">
        <table class="capacity-table">
          <thead>
            <!-- 第一行表头 -->
            <tr>
              <th rowspan="2">排放口编号</th>
              <th rowspan="2">排放口名称</th>
              <th rowspan="2">污染物种类</th>
              <th colspan="2">排放口地理坐标</th>
              <th rowspan="2">排气筒高度(m)</th>
              <th rowspan="2">排气口出口内径(m)</th>
              <th rowspan="2">排气温度</th>
              <th rowspan="2">其他信息</th>
            </tr>
            <!-- 第二行表头 -->
            <tr>
              <th>经度</th>
              <th>纬度</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="data.length === 0">
              <td colspan="9" class="no-data">暂无数据</td>
            </tr>
            <tr v-for="item in data" :key="item.id">
              <!--  排放口编号-->
              <td>{{ item.drainCode || "-" }}</td>
              <!--  排放口名称-->
              <td>{{ item.drainName || "-" }}</td>
              <!--  污染物种类-->
              <td>{{ item.wrwName || "-" }}</td>
              <!--  经度-->
              <td>{{ item.longitude || "-" }}</td>
              <!--  纬度-->
              <td>{{ item.latitude || "-" }}</td>
              <!--  排气筒高度(m)-->
              <td>{{ item.exhaustHeight || "-" }}</td>
              <!--  排气口出口内径(m)-->
              <td>{{ item.exhaustBore || "-" }}</td>
              <!--  排气温度-->
              <td>{{ item.temperatureName || "-" }}</td>
              <!--  其他信息-->
              <td>{{ item.otherContent || "-" }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AirOutletDetail",
  props: {
    raw: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    data() {
      if (this.raw && this.raw.airOutletBasicInfosList) {
        return this.raw.airOutletBasicInfosList;
      } else {
        return [];
      }
    }
  },
  methods: {}
};
</script>

<style scoped lang="scss">
.detail-container {
  height: 100%;

  .detail-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
  }

  .detail-content {
    height: calc(100% - 60px);
    padding: 16px;
    overflow: auto;

    .table-container {
      width: 100%;
      overflow-x: auto;

      .capacity-table {
        width: 100%;
        min-width: 1200px; // 确保表格有足够宽度显示所有列
        font-size: 12px;
        table-layout: fixed;
        border-collapse: collapse;
        border: 1px solid #dddddd;

        th {
          padding: 8px 12px;
          font-weight: 500;
          color: #333333;
          text-align: center;
          background-color: #f5f5f5;
          border: 1px solid #dddddd;
        }

        td {
          padding: 8px 12px;
          color: #333333;
          text-align: center;
          word-break: break-all;
          vertical-align: middle;
          background-color: #ffffff;
          border: 1px solid #dddddd;

          &.no-data {
            font-style: italic;
            color: #999999;
            text-align: center;
          }
        }

        tbody tr {
          min-height: 40px;

          &:hover {
            background-color: #fafafa;

            td {
              background-color: #fafafa;
            }
          }
        }
      }
    }

    // 说明部分样式
    .instructions {
      padding: 12px 16px;
      margin-bottom: 8px;
      color: #007bff;
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;

      .instruction-header {
        margin-bottom: 4px;
        font-size: 14px;
      }

      .instruction-item {
        margin-bottom: 4px;
        font-size: 12px;
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
