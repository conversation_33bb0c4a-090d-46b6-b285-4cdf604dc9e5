<template>
  <div class="detail-container">
    <div class="detail-header">
      <h3>大气污染物排放信息-企业大气排放总许可量</h3>
    </div>
    <div class="detail-content">
      <!-- 说明部分 -->
      <div class="instructions">
        <header class="instruction-header">说明</header>
        <div class="instruction-item">
          (1)
          “全厂合计"指的是，“全厂有组织排放总计"与“全厂无组织排放总计”之和数据、全厂总量控制指标数据两者取严。
        </div>
        <div class="instruction-item">
          (2)
          系统自动计算“全厂有组织排放总计"与“全厂无组织排放总计“之和，请根据贵单位全厂总量控制指标数据对“全厂合计值进行核对与修改。
        </div>
      </div>
      <div class="table-container">
        <table class="capacity-table">
          <thead>
            <!-- 第一行表头 -->
            <tr>
              <th rowspan="2">污染物种类</th>
              <th colspan="5">全厂合计（t/a）</th>
            </tr>
            <!-- 第二行表头 -->
            <tr>
              <th>第一年</th>
              <th>第二年</th>
              <th>第三年</th>
              <th>第四年</th>
              <th>第五年</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="data.length === 0">
              <td colspan="6" class="no-data">暂无数据</td>
            </tr>
            <tr v-for="item in data" :key="item.id">
              <!--  污染物种类-->
              <td>{{ item.wrwName || "-" }}</td>
              <!--  第一年（许可第一年年排放量限值）-->
              <td>{{ item.xkOneYear || "-" }}</td>
              <!--  第二年（许可第二年年排放量限值）-->
              <td>{{ item.xkTwoYear || "-" }}</td>
              <!--  第三年（许可第三年年排放量限值）-->
              <td>{{ item.xkThreeYear || "-" }}</td>
              <!--  第四年（许可第四年年排放量限值）-->
              <td>{{ item.xkFourYear || "-" }}</td>
              <!--  第五年（许可第五年年排放量限值）-->
              <td>{{ item.xkFiveYear || "-" }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AirEmissionDetail",
  props: {
    raw: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    data() {
      if (this.raw && this.raw.fswrwQcEmissInfo) {
        return this.raw.fswrwQcEmissInfo;
      } else {
        return [];
      }
    }
  },
  methods: {}
};
</script>

<style scoped lang="scss">
.detail-container {
  height: 100%;

  .detail-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
  }

  .detail-content {
    height: calc(100% - 60px);
    padding: 16px;
    overflow: auto;

    .table-container {
      width: 100%;
      overflow-x: auto;

      .capacity-table {
        width: 100%;
        min-width: 1200px; // 确保表格有足够宽度显示所有列
        font-size: 12px;
        table-layout: fixed;
        border-collapse: collapse;
        border: 1px solid #dddddd;

        th {
          padding: 8px 12px;
          font-weight: 500;
          color: #333333;
          text-align: center;
          background-color: #f5f5f5;
          border: 1px solid #dddddd;
        }

        td {
          padding: 8px 12px;
          color: #333333;
          text-align: center;
          word-break: break-all;
          vertical-align: middle;
          background-color: #ffffff;
          border: 1px solid #dddddd;

          &.no-data {
            font-style: italic;
            color: #999999;
            text-align: center;
          }
        }

        tbody tr {
          min-height: 40px;

          &:hover {
            background-color: #fafafa;

            td {
              background-color: #fafafa;
            }
          }
        }
      }
    }

    // 说明部分样式
    .instructions {
      padding: 12px 16px;
      margin-bottom: 8px;
      color: #007bff;
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;

      .instruction-header {
        margin-bottom: 4px;
        font-size: 14px;
      }

      .instruction-item {
        margin-bottom: 4px;
        font-size: 12px;
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
