<template>
  <div class="detail-container">
    <div class="detail-header">
      <h3>水污染物排放信息-申请排放信息</h3>
    </div>
    <div class="detail-content">
      <header style="margin-bottom: 12px; font-size: 14px; color: #42b2de;">
        一、主要排放口
      </header>
      <!-- 说明部分 -->
      <div class="instructions">
        <header class="instruction-header">说明：</header>
        <div class="instruction-item">
          (1) 排入城镇集中污水处理设施的生活污水无需申请许可排放量。
        </div>
        <div class="instruction-item">
          (2) 浓度限值未显示单位的，默认单位为"mg/L"。
        </div>
      </div>
      <div class="table-container">
        <table class="capacity-table">
          <thead>
            <!-- 第一行表头 -->
            <tr>
              <th rowspan="2">排放口编号</th>
              <th rowspan="2">排放口名称</th>
              <th rowspan="2">污染物种类</th>
              <th rowspan="2">申请排放浓度限值</th>
              <th colspan="5">申请年排放量限值（t/a）</th>
            </tr>
            <!-- 第二行表头 -->
            <tr>
              <th>第一年</th>
              <th>第二年</th>
              <th>第三年</th>
              <th>第四年</th>
              <th>第五年</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="mainOutletEmissInfo.length === 0">
              <td colspan="9" class="no-data">暂无数据</td>
            </tr>
            <tr v-for="item in mainOutletEmissInfo" :key="item.id">
              <!--  排放口编号-->
              <td>{{ item.drainCode || "-" }}</td>
              <!--  排放口名称-->
              <td>{{ item.drainName || "-" }}</td>
              <!--  污染物种类-->
              <td>{{ item.wrwName || "-" }}</td>
              <!--  申请排放浓度限值-->
              <td v-if="item.xkEmissionCon">
                {{ item.xkEmissionCon + item.xkEmissionConDwName }}
              </td>
              <td v-else>-</td>
              <!--  第一年（许可第一年年排放量限值）-->
              <td>{{ item.xkOneYear || "-" }}</td>
              <!--  第二年（许可第二年年排放量限值）-->
              <td>{{ item.xkTwoYear || "-" }}</td>
              <!--  第三年（许可第三年年排放量限值）-->
              <td>{{ item.xkThreeYear || "-" }}</td>
              <!--  第四年（许可第四年年排放量限值）-->
              <td>{{ item.xkFourYear || "-" }}</td>
              <!--  第五年（许可第五年年排放量限值）-->
              <td>{{ item.xkFiveYear || "-" }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!--      全厂排放口表格-->
      <header
        style="margin-top: 30px; margin-bottom: 12px; font-size: 14px; color: #42b2de;"
      >
        二、全厂排放口总计
      </header>

      <div class="table-container">
        <table class="capacity-table">
          <thead>
            <!-- 第一行表头 -->
            <tr>
              <th rowspan="2">污染物种类</th>
              <th colspan="5">申请年排放量限值（t/a）</th>
            </tr>
            <!-- 第二行表头 -->
            <tr>
              <th>第一年</th>
              <th>第二年</th>
              <th>第三年</th>
              <th>第四年</th>
              <th>第五年</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="fqwrwQcxk.length === 0">
              <td colspan="6" class="no-data">暂无数据</td>
            </tr>
            <tr v-for="item in fqwrwQcxk" :key="item.id">
              <!--  污染物种类-->
              <td>{{ item.wrwName || "-" }}</td>
              <!--  第一年（全厂合计第一年）-->
              <td>{{ item.qcOneYear || "-" }}</td>
              <!--  第二年（全厂合计第二年）-->
              <td>{{ item.qcTwoYear || "-" }}</td>
              <!--  第三年（全厂合计第三年）-->
              <td>{{ item.qcThreeYear || "-" }}</td>
              <!--  第四年（全厂合计第四年）-->
              <td>{{ item.qcFourYear || "-" }}</td>
              <!--  第五年（全厂合计第五年）-->
              <td>{{ item.qcFiveYear || "-" }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "WaterEmissionDetail",
  props: {
    raw: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    mainOutletEmissInfo() {
      if (this.raw && this.raw.mainOutletEmissInfo) {
        return this.raw.mainOutletEmissInfo;
      } else {
        return [];
      }
    },
    fqwrwQcxk() {
      if (this.raw && this.raw.fqwrwQcxk) {
        return this.raw.fqwrwQcxk;
      } else {
        return [];
      }
    }
  },
  methods: {}
};
</script>

<style scoped lang="scss">
.detail-container {
  height: 100%;

  .detail-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
  }

  .detail-content {
    height: calc(100% - 60px);
    padding: 16px;
    overflow: auto;

    .table-container {
      width: 100%;
      overflow-x: auto;

      .capacity-table {
        width: 100%;
        min-width: 1200px; // 确保表格有足够宽度显示所有列
        font-size: 12px;
        table-layout: fixed;
        border-collapse: collapse;
        border: 1px solid #dddddd;

        th {
          padding: 8px 12px;
          font-weight: 500;
          color: #333333;
          text-align: center;
          background-color: #f5f5f5;
          border: 1px solid #dddddd;
        }

        td {
          padding: 8px 12px;
          color: #333333;
          text-align: center;
          word-break: break-all;
          vertical-align: middle;
          background-color: #ffffff;
          border: 1px solid #dddddd;

          &.no-data {
            font-style: italic;
            color: #999999;
            text-align: center;
          }
        }

        tbody tr {
          min-height: 40px;

          &:hover {
            background-color: #fafafa;

            td {
              background-color: #fafafa;
            }
          }
        }
      }
    }

    // 说明部分样式
    .instructions {
      padding: 12px 16px;
      margin-bottom: 8px;
      color: #007bff;
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;

      .instruction-header {
        margin-bottom: 4px;
        font-size: 14px;
      }

      .instruction-item {
        margin-bottom: 4px;
        font-size: 12px;
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
