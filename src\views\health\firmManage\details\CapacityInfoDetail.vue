<template>
  <div class="detail-container">
    <div class="detail-header">
      <h3>主要产品及产能补充</h3>
    </div>
    <div class="detail-content">
      <!-- 说明部分 -->
      <div class="instructions">
        <header class="instruction-header">说明</header>
        <div class="instruction-item"> (1) 本表格适用于部分行业，您可在行业类别选择框中选到对应行业。若无法选到某个行业，说明此行业不用填写本表格。</div>
        <div class="instruction-item"> (2) 若本单位涉及多个行业，请分别对每个行业进行添加设置。</div>
      </div>
      <div class="table-container">

        <table class="capacity-table">
          <thead>
            <!-- 第一行表头 -->
            <tr>
              <th rowspan="2">生产编号和名称</th>
              <th rowspan="2">主要生产单元名称</th>
              <th rowspan="2">主要工艺名称</th>
              <th rowspan="2">生产设施名称</th>
              <th rowspan="2">生产设施编号</th>
              <th colspan="4">设施参数</th>
              <th rowspan="2">生产能力</th>
              <th rowspan="2">其他设施信息</th>
              <th rowspan="2">其他工艺信息</th>
            </tr>
            <!-- 第二行表头 -->
            <tr>
              <th>参数名称</th>
              <th>计量单位</th>
              <th>设计值</th>
              <th>其他设施参数信息</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="!data || data.length === 0">
              <td colspan="14" class="no-data">暂无数据</td>
            </tr>
            <tr v-for="item in data" :key="item.id">
              <!--  生产编号和名称-->
              <td>{{ item.productName || "-" }}</td>
              <!--  主要生产单元名称-->
              <td>{{ item.scdyName || "-" }}</td>
              <!--  主要工艺名称-->
              <td>{{ item.technicsName || "-" }}</td>
              <!--  生产设施名称-->
              <td>{{ item.deviceName || "-" }}</td>
              <!--  生产设施编号-->
              <td>{{ item.deviceCode || "-" }}</td>
              <!--  设施参数-->
              <td>{{ item.paramName || "-" }}</td>
              <!--  计量单位-->
              <td>{{ item.paramUnitsName || "-" }}</td>
              <!--  设计值-->
              <td>{{ item.paramDesign || "-" }}</td>
              <!--  其他设施参数信息-->
              <td>{{ item.capaContent || "-" }}</td>
              <!--  生产能力-->
              <td>{{ item.prodCapacity || "-" }}</td>
              <!--  其他设施信息-->
              <td>{{ item.facContent || "-" }}</td>
              <!--  其他工艺信息-->
              <td>{{ item.technicsContent || "-" }}</td>
            </tr>
          </tbody>
        </table>
      </div>

    </div>
  </div>
</template>

<script>
export default {
  name: "CapacityInfoDetail",
  props: {
    raw: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    data() {
      return this.raw.capacityInfoList || []
    }
  },
  methods: {}
};
</script>

<style scoped lang="scss">
.detail-container {
  height: 100%;

  .detail-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
  }

  .detail-content {
    height: calc(100% - 60px);
    padding: 16px;
    overflow: auto;

    .table-container {
      width: 100%;
      overflow-x: auto;

      .capacity-table {
        width: 100%;
        min-width: 1200px; // 确保表格有足够宽度显示所有列
        font-size: 12px;
        table-layout: fixed;
        border-collapse: collapse;
        border: 1px solid #dddddd;

        th {
          padding: 8px 12px;
          font-weight: 500;
          color: #333333;
          text-align: center;
          background-color: #f5f5f5;
          border: 1px solid #dddddd;
        }

        td {
          padding: 8px 12px;
          color: #333333;
          text-align: center;
          word-break: break-all;
          vertical-align: middle;
          background-color: #ffffff;
          border: 1px solid #dddddd;

          &.no-data {
            font-style: italic;
            color: #999999;
            text-align: center;
          }
        }

        tbody tr {
          min-height: 40px;

          &:hover {
            background-color: #fafafa;

            td {
              background-color: #fafafa;
            }
          }
        }
      }
    }

    // 说明部分样式
    .instructions {
      padding: 12px 16px;
      margin-bottom: 16px;
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      color: #007bff;

      .instruction-header {
        font-size: 14px;
        margin-bottom: 8px;
      }

      .instruction-item {
        margin-bottom: 8px;
        font-size: 12px;
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
